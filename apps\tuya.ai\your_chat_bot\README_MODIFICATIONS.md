# Your Chat Bot - 舵机控制修改版

## 📋 项目概述
这是基于 TuyaOpen 的智能聊天机器人项目，已修改为支持按钮控制 PCA9685 舵机门。

## 🔧 硬件配置
- **主控**：涂鸦开发板
- **舵机驱动**：PCA9685 (I2C地址: 0x40)
- **舵机**：MG90S (连接到 PCA9685 通道 0)
- **I2C引脚**：
  - SDA: P06 (TUYA_GPIO_NUM_6)
  - SCL: P07 (TUYA_GPIO_NUM_7)

## 🎮 按钮功能
- **单击**：控制舵机门开关（开启90°/关闭0°）
- **双击**：触发聊天功能（原单击功能）
- **长按**：紧急停止所有舵机

## 📁 主要修改文件
1. `src/app_chat_bot.c` - 修改按钮事件处理
2. `src/app_button_control.c` - 新增按钮控制模块
3. `include/app_button_control.h` - 按钮控制头文件
4. `src/pca9685_driver.c` - PCA9685驱动
5. `src/pwm_door_control.c` - 舵机门控制

## 🚀 编译和使用
```bash
cd apps/tuya.ai/your_chat_bot
make clean
make
make flash
```

## 📊 功能特性
- ✅ PCA9685 I2C 舵机驱动
- ✅ 按钮控制门开关
- ✅ 紧急停止功能
- ✅ 状态显示和反馈
- ✅ 聊天功能保留（双击触发）
- ✅ 统计信息记录

## 🔍 调试信息
- 按钮事件会在串口输出详细日志
- 舵机控制状态实时显示
- 支持运行时状态查询

## 📝 版本历史
- v1.0: 基础聊天功能
- v2.0: 添加舵机控制功能
- v2.1: 优化按钮事件处理

## 🛠️ 文件替换步骤

### 1. 备份原项目
```bash
cp -r apps/tuya.ai/your_chat_bot apps/tuya.ai/your_chat_bot_backup_$(date +%Y%m%d_%H%M%S)
```

### 2. 替换文件
```bash
# 方法1：完整替换
mv apps/tuya.ai/your_chat_bot apps/tuya.ai/your_chat_bot_original
cp -r /path/to/modified/your_chat_bot apps/tuya.ai/your_chat_bot

# 方法2：选择性替换
cp /path/to/modified/src/app_chat_bot.c apps/tuya.ai/your_chat_bot/src/
cp /path/to/modified/src/app_button_control.c apps/tuya.ai/your_chat_bot/src/
cp /path/to/modified/include/app_button_control.h apps/tuya.ai/your_chat_bot/include/
```

### 3. 验证和编译
```bash
cd apps/tuya.ai/your_chat_bot
make clean && make
```

## 📞 技术支持
如有问题，请检查：
1. 硬件连接是否正确
2. I2C引脚配置是否匹配
3. 编译是否成功
4. 串口日志输出
